"use client";
import React from "react";
import { Drawer, Drawer<PERSON>ontent, <PERSON>er<PERSON><PERSON>, DrawerHeader } from "@heroui/drawer";

interface AdjacentDrawerProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  children: React.ReactNode;
}

const AdjacentDrawer: React.FC<AdjacentDrawerProps> = ({
  isOpen,
  onOpenChange,
  title,
  children,
}) => {
  return (
    <Drawer
      isDismissable={false}
      isKeyboardDismissDisabled={true}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      placement="left"
      size="md"
      radius="none"
      hideCloseButton={false}
      backdrop="transparent"
      classNames={{
        closeButton: "drawer-close-button",
        base: "adjacent-drawer !rounded-none",
      }}
    >
      <DrawerContent className="drawer-content !rounded-none">
        {() => (
          <>
            <DrawerHeader className="px-4 pt-4 border-b">
              <div className="flex justify-between items-center mb-2 w-full">
                <div
                  className="cursor-pointer text-base font-medium text-black flex items-center gap-2"
                  onClick={() => onOpenChange(false)}
                >
                  <img src="/assets/left-arrow.svg" alt="" />
                  Back
                </div>
                <p className="text-base font-bold">{title}</p>
                <div className="w-10"></div> {/* Spacer for alignment */}
              </div>
            </DrawerHeader>
            <DrawerBody className="h-[500px] overflow-y-auto chat-scroll-custom pb-12 max-md:pb-16 px-4 mt-4">
              {children}
            </DrawerBody>
          </>
        )}
      </DrawerContent>
    </Drawer>
  );
};

export default AdjacentDrawer;
