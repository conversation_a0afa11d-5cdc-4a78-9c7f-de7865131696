import React, { useState } from "react";
import NestedDrawer from "../ui/nested-drawer";
import { CheckCircle, Circle } from "react-feather";
import { UpdateActivityLog } from "@/services/ordersServices";
import { OrderStatusType } from "@/lib/constant";
import { getIOSInputStyles } from "@/utils/iosUtils";

interface ChangeStatusDrawerProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  currentStatus: string;
  onStatusChange: (status: string) => void;
  orderType: "placed" | "received";
  orderId?: string;
  loggedInUser?: string;
  sellerName?: string;
  userName?: string;
}

const placedStatuses = [
  { id: "revision_request", label: "Revision request" },
  { id: "completed", label: "Completed" },
];

const receivedStatuses = [
  { id: "accept", label: "Accept" },
  { id: "incomplete", label: "Incomplete" },
  { id: "decline", label: "Decline" },
];

const acceptedStatuses = [{ id: "delivered", label: "Delivered" }];

const incompleteStatuses = [
  { id: "accept", label: "Accept" },
  { id: "decline", label: "Decline" },
];

const declinedStatuses: { id: string; label: string }[] = [
  // No further actions available after decline
];

const ChangeStatusDrawer: React.FC<ChangeStatusDrawerProps> = ({
  isOpen,
  onOpenChange,
  currentStatus,
  onStatusChange,
  orderType,
  orderId,
  loggedInUser,
  sellerName,
  userName,
}) => {
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);
  const [comment, setComment] = useState("");
  const [declineReasons, setDeclineReasons] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const declineOptions = [
    "I am not able to deliver the order",
    "I am currently overloaded with other orders",
    "Other",
  ];

  // Function to determine which statuses to show based on current status and order type
  const getAvailableStatuses = () => {
    if (orderType === "received") {
      // For received orders, check current status
      switch (currentStatus.toLowerCase()) {
        case "new":
        case "basket":
          return receivedStatuses; // Accept, Incomplete, Decline
        case "accepted":
          return acceptedStatuses; // Delivered
        case "revision request":
          return acceptedStatuses; // Completed
        case "incomplete":
          return incompleteStatuses; // Accept, Decline
        case "declined":
          return declinedStatuses; // No further actions
        default:
          return [];
      }
    } else if (orderType === "placed") {
      // For placed orders, we'll implement this later
      switch (currentStatus.toLowerCase()) {
        case "delivered":
          return placedStatuses; // No further actions
      }
    }

    return [];
  };

  // Map component status to OrderStatusType
  const mapStatusToOrderStatusType = (status: string): OrderStatusType => {
    switch (status) {
      case "accept":
        return OrderStatusType.ACCEPTED;
      case "decline":
        return OrderStatusType.DECLINED;
      case "incomplete":
        return OrderStatusType.INCOMPLETE;
      case "revision_request":
        return OrderStatusType.REVISION_REQUEST;
      case "completed":
        return OrderStatusType.COMPLETED;
      case "delivered":
        return OrderStatusType.DELIVERED;
      default:
        return OrderStatusType.NEW;
    }
  };

  const handleStatusConfirmation = async () => {
    // Call UpdateActivityLog service if all required data is available
    if (orderId && loggedInUser && sellerName && userName) {
      try {
        const orderStatusType = mapStatusToOrderStatusType(selectedStatus);
        const reason =
          selectedStatus === "decline"
            ? declineReasons.join(", ")
            : comment.trim() || "Status updated";

        const response = await UpdateActivityLog({
          orderId,
          description: comment.trim(),
          from: orderType === "received" ? "creator" : "user",
          title: orderStatusType,
          loggedInUser,
          sellerName,
          userName,
          reason,
        });
        // console.log('t1',{response})
        console.log("Activity log updated successfully");
        return { success: true, response };
      } catch (error) {
        console.error("Failed to update activity log:", error);
        console.error("Error details:", error);
        return { success: false, error };
      }
    } else {
      console.warn("Missing required data for UpdateActivityLog:", {
        orderId,
        loggedInUser,
        sellerName,
        userName,
      });
      return { success: false, error: "Missing required data" };
    }
  };

  const handleConfirm = async () => {
    setIsLoading(true);

    try {
      // Call the confirmation function
      const result = await handleStatusConfirmation();

      // Only execute the original functionality if API call was successful
      if (result.success) {
        onStatusChange(selectedStatus);
        onOpenChange(false);
      } else {
        // Keep the drawer open and optionally show an error message
        console.log("Keeping drawer open due to API failure");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <NestedDrawer isOpen={isOpen} onOpenChange={onOpenChange} title="Change Status">
      <div className="flex flex-col h-full bg-white">
        {/* Content */}
        <div className="flex-1 md:p-4 overflow-y-auto">
          {/* Status Selection */}
          <div className="flex flex-row gap-2 items-center">
            <h3 className="text-sm font-semibold text-gray-600">Status</h3>
            <div className="flex flex-row gap-2 items-center">
              {/* Dynamic status selection based on current status and order type */}
              <div>
                {getAvailableStatuses().map((status) => (
                  <button
                    key={status.id}
                    onClick={() => setSelectedStatus(status.id)}
                    className={`px-4 py-2.5 text-sm transition-colors rounded-lg mr-2 ${
                      selectedStatus === status.id
                        ? "bg-black text-white font-medium"
                        : "bg-[#F5F5F5] text-gray-900 hover:bg-gray-200"
                    }`}
                  >
                    {status.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
          <div>
            {selectedStatus === "accept" && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                You're confirming that you're accepting the order. The buyer will receive a
                confirmation message.
              </div>
            )}
            {selectedStatus === "incomplete" && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                Incomplete order information. You can request additional information within 48 hours
                of receiving the order
              </div>
            )}
            {selectedStatus === "decline" && (
              <>
                <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                  You're about to reject this order. Please let the buyer know why.
                </div>
                <div className="mt-2 mb-2">
                  <h4 className="text-sm font-medium mb-2">Reason</h4>
                  {declineOptions.map((reason) => (
                    <label
                      key={reason}
                      className={`flex items-center gap-2 mb-1 text-sm cursor-pointer ${declineReasons.includes(reason) ? "text-primary" : "text-[#828282]"}`}
                    >
                      <input
                        type="checkbox"
                        value={reason}
                        checked={declineReasons.includes(reason)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setDeclineReasons([...declineReasons, reason]);
                          } else {
                            setDeclineReasons(declineReasons.filter((r) => r !== reason));
                          }
                        }}
                        className="hidden"
                      />
                      {declineReasons.includes(reason) ? <CheckCircle /> : <Circle />}
                      {reason}
                    </label>
                  ))}
                </div>
              </>
            )}

            {selectedStatus === "revision_request" && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                The work is not yet delivered, or accepted as delivered. The task needs to be
                completed or revised.
              </div>
            )}
            {selectedStatus === "completed" && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                You're confirming that the work has been completed and is accepted.
              </div>
            )}
            {selectedStatus === "delivered" && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                You have delivered the order. The buyer will receive a confirmation for their review
                and acceptance.
              </div>
            )}
          </div>

          {/* Comment Field */}
          <div className="mt-6">
            <h3 className="text-sm font-normal text-gray-600 mb-3">Comment</h3>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Your comment"
              className="w-full h-[120px] p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-black focus:border-black placeholder:text-gray-400"
              style={getIOSInputStyles()}
            />
          </div>
        </div>

        {/* Footer with Confirm Button - Sticky for iOS compatibility */}
        <div className="md:p-4 border-t border-gray-100 bg-white sticky bottom-0 z-10">
          <button
            onClick={handleConfirm}
            className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-2"
            disabled={!selectedStatus || !comment.trim() || isLoading}
          >
            {isLoading ? "Processing..." : "Confirm"}
          </button>
        </div>
      </div>
    </NestedDrawer>
  );
};

export default ChangeStatusDrawer;
