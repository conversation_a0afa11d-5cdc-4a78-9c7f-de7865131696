// Client-side Stripe publishable key helpers
// This file is safe to import in client components. Do NOT import the server 'stripe' package here.

export const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string | undefined;
export const PUBLISHABLE_KEY_US = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_US as string | undefined;

export function isUSCurrency(currency: string): boolean {
  return (currency || '').toUpperCase() === 'USD';
}

export function getPublishableKey(isUS: boolean = false): string {
  const key = isUS ? (PUBLISHABLE_KEY_US ?? PUBLISHABLE_KEY) : (PUBLISHABLE_KEY ?? PUBLISHABLE_KEY_US);
  if (!key) {
    // Return empty string to avoid initializing Stripe with undefined; caller should guard and show UI message
    console.warn(`Stripe publishable key is not configured for ${isUS ? 'US' : 'Global'} environment.`);
    return '';
  }
  return key;
}

export function getPublishableKeyByCurrency(currency: string): { publishableKey: string; isUS: boolean } {
  const isUsFlag = isUSCurrency(currency);
  return {
    publishableKey: getPublishableKey(isUsFlag),
    isUS: isUsFlag,
  };
}

