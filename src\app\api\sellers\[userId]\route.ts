import { NextRequest, NextResponse } from 'next/server';
import { initFirebase } from '../../../../../firebaseConfig';
import { doc, getDoc } from 'firebase/firestore';
import { getStripeInstance } from '@/lib/stripe';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    console.log('Fetching seller account for user:', userId);

    // Initialize Firebase
    const { db } = await initFirebase();

    // Get user document to check for stripe_id
    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);

    if (!userDoc.exists()) {
      console.log('User document not found:', userId);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();
    const stripeAccountId = userData.stripe_id;
    const isUS = Boolean(userData.isUS);
    const stripeClient = getStripeInstance(isUS);

    if (!stripeAccountId) {
      console.log('No stripe_id found for user:', userId);
      return NextResponse.json(
        { error: 'No seller account found' },
        { status: 404 }
      );
    }

    console.log('Found stripe_id:', stripeAccountId);

    // Fetch account details from Stripe
    try {
      const account = await stripeClient.accounts.retrieve(stripeAccountId);

      // Also get the stripeAccounts document for additional metadata
      let stripeAccountData = null;
      try {
        const stripeAccountRef = doc(db, 'stripeAccounts', stripeAccountId);
        const stripeAccountDoc = await getDoc(stripeAccountRef);
        if (stripeAccountDoc.exists()) {
          stripeAccountData = stripeAccountDoc.data();
        }
      } catch (err) {
        console.log('Could not fetch stripeAccounts document:', err);
      }

      // Format the response to match what the Connect components expect
      const sellerAccount = {
        stripeAccountId: account.id,
        onboardingComplete: account.details_submitted || false,
        chargesEnabled: account.charges_enabled || false,
        payoutsEnabled: account.payouts_enabled || false,
        email: account.email || stripeAccountData?.email,
        businessName: account.business_profile?.name || account.display_name,
        country: account.country,
        currency: account.default_currency,
        created: account.created,
        type: account.type,
        status: {
          chargesEnabled: account.charges_enabled,
          payoutsEnabled: account.payouts_enabled,
          detailsSubmitted: account.details_submitted,
        },
        requirements: {
          currentlyDue: account.requirements?.currently_due || [],
          eventuallyDue: account.requirements?.eventually_due || [],
          pastDue: account.requirements?.past_due || [],
          pendingVerification: account.requirements?.pending_verification || [],
        },
        // Include metadata from stripeAccounts collection
        metadata: stripeAccountData ? {
          createdAt: stripeAccountData.createdAt,
          accountType: stripeAccountData.accountType,
          onboardingComplete: stripeAccountData.onboardingComplete
        } : null
      };

      console.log('Successfully retrieved seller account:', {
        accountId: account.id,
        onboardingComplete: sellerAccount.onboardingComplete,
        chargesEnabled: sellerAccount.chargesEnabled
      });

      return NextResponse.json(sellerAccount);

    } catch (stripeError) {
      console.error('Error fetching Stripe account:', stripeError);

      // If account doesn't exist in Stripe, return 404
      if (stripeError instanceof Error && stripeError.message.includes('No such account')) {
        console.log('Stripe account not found');
        return NextResponse.json(
          { error: 'Seller account not found in Stripe' },
          { status: 404 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch seller account details' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in GET /api/sellers/[userId]:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
